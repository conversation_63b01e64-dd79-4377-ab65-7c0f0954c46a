"use client";

import React, { useState, useRef, useCallback } from 'react';
import ReactCrop, { Crop, PixelCrop, centerCrop, makeAspectCrop } from 'react-image-crop';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Upload, Download } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useTranslations } from 'next-intl';
import 'react-image-crop/dist/ReactCrop.css';

interface CircleCropProps {
  className?: string;
}

export default function CircleCrop({ className }: CircleCropProps) {
  const t = useTranslations('circle_crop');
  const fileInputRef = useRef<HTMLInputElement>(null);
  const imgRef = useRef<HTMLImageElement>(null);
  
  const [image, setImage] = useState<string>('');
  const [crop, setCrop] = useState<Crop>();
  const [completedCrop, setCompletedCrop] = useState<PixelCrop>();
  const [isUploading, setIsUploading] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string>('');
  const [isDragOver, setIsDragOver] = useState(false);
  const [imageDisplaySize, setImageDisplaySize] = useState<{width: number, height: number}>({width: 0, height: 0});
  const [scale, setScale] = useState<number>(1);

  // 支持的文件类型
  const supportedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
  const maxFileSize = 10 * 1024 * 1024; // 10MB

  // 计算合适的显示尺寸
  const calculateDisplaySize = useCallback((naturalWidth: number, naturalHeight: number) => {
    // 获取容器的最大可用空间
    const maxWidth = Math.min(typeof window !== 'undefined' ? window.innerWidth * 0.8 : 800, 800);
    const maxHeight = Math.min(typeof window !== 'undefined' ? window.innerHeight * 0.6 : 500, 600);

    // 计算缩放比例，保持宽高比
    const scaleX = maxWidth / naturalWidth;
    const scaleY = maxHeight / naturalHeight;
    const scale = Math.min(scaleX, scaleY, 1); // 不放大，只缩小

    return {
      width: Math.round(naturalWidth * scale),
      height: Math.round(naturalHeight * scale),
      scale
    };
  }, []);

  // 验证裁剪坐标是否有效
  const validateCropCoordinates = useCallback((crop: PixelCrop, imageWidth: number, imageHeight: number) => {
    return (
      crop.x >= 0 &&
      crop.y >= 0 &&
      crop.x + crop.width <= imageWidth &&
      crop.y + crop.height <= imageHeight &&
      crop.width > 0 &&
      crop.height > 0
    );
  }, []);

  const validateFile = (file: File): boolean => {
    if (!supportedTypes.includes(file.type)) {
      setError(t('error_file_type'));
      return false;
    }
    if (file.size > maxFileSize) {
      setError(t('error_file_size'));
      return false;
    }
    return true;
  };

  const handleFileUpload = useCallback((file: File) => {
    if (!validateFile(file)) return;

    setIsUploading(true);
    setError('');

    const reader = new FileReader();
    reader.onload = () => {
      setImage(reader.result as string);
      setIsUploading(false);
    };
    reader.onerror = () => {
      setError(t('error_upload'));
      setIsUploading(false);
    };
    reader.readAsDataURL(file);
  }, [t]);

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      handleFileUpload(file);
    }
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragOver(false);
    
    const file = e.dataTransfer.files[0];
    if (file) {
      handleFileUpload(file);
    }
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const onImageLoad = (e: React.SyntheticEvent<HTMLImageElement>) => {
    const { naturalWidth, naturalHeight } = e.currentTarget;

    // 计算合适的显示尺寸
    const displaySize = calculateDisplaySize(naturalWidth, naturalHeight);
    setImageDisplaySize(displaySize);
    setScale(displaySize.scale);

    // 创建一个正方形的裁剪区域，居中显示
    const crop = centerCrop(
      makeAspectCrop(
        {
          unit: '%',
          width: 80,
        },
        1, // 1:1 aspect ratio for circle
        naturalWidth,
        naturalHeight
      ),
      naturalWidth,
      naturalHeight
    );

    setCrop(crop);
  };

  const getCroppedImg = useCallback(async (): Promise<string | null> => {
    if (!completedCrop || !imgRef.current) return null;

    const image = imgRef.current;
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');

    if (!ctx) return null;

    // 验证裁剪坐标
    if (!validateCropCoordinates(completedCrop, image.naturalWidth, image.naturalHeight)) {
      console.error('Invalid crop coordinates:', completedCrop);
      return null;
    }

    const scaleX = image.naturalWidth / image.width;
    const scaleY = image.naturalHeight / image.height;

    // 设置canvas尺寸为裁剪区域的实际像素尺寸
    canvas.width = completedCrop.width;
    canvas.height = completedCrop.height;

    // 绘制裁剪的图片
    ctx.drawImage(
      image,
      completedCrop.x * scaleX,
      completedCrop.y * scaleY,
      completedCrop.width * scaleX,
      completedCrop.height * scaleY,
      0,
      0,
      completedCrop.width,
      completedCrop.height
    );

    // 创建圆形遮罩
    const radius = Math.min(completedCrop.width, completedCrop.height) / 2;
    const centerX = completedCrop.width / 2;
    const centerY = completedCrop.height / 2;

    // 获取图像数据
    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
    const data = imageData.data;

    // 应用圆形遮罩
    for (let y = 0; y < canvas.height; y++) {
      for (let x = 0; x < canvas.width; x++) {
        const distance = Math.sqrt((x - centerX) ** 2 + (y - centerY) ** 2);
        if (distance > radius) {
          const index = (y * canvas.width + x) * 4;
          data[index + 3] = 0; // 设置alpha为0（透明）
        }
      }
    }

    // 将修改后的图像数据放回canvas
    ctx.putImageData(imageData, 0, 0);

    return new Promise((resolve) => {
      canvas.toBlob((blob) => {
        if (blob) {
          resolve(URL.createObjectURL(blob));
        } else {
          resolve(null);
        }
      }, 'image/png');
    });
  }, [completedCrop]);

  const handleDownload = async () => {
    if (!completedCrop) return;

    setIsProcessing(true);
    setError('');

    try {
      const croppedImageUrl = await getCroppedImg();
      if (croppedImageUrl) {
        const link = document.createElement('a');
        link.download = 'circle-cropped-image.png';
        link.href = croppedImageUrl;
        link.click();
        URL.revokeObjectURL(croppedImageUrl);
      } else {
        setError(t('error_crop'));
      }
    } catch (err) {
      setError(t('error_crop'));
    } finally {
      setIsProcessing(false);
    }
  };

  const handleUploadNew = () => {
    fileInputRef.current?.click();
  };

  const handleUploadClick = () => {
    fileInputRef.current?.click();
  };

  return (
    <div className={cn("w-full max-w-4xl mx-auto", className)}>
      <Card>
        {/* <CardHeader>
          <CardTitle className="text-center">{t('title')}</CardTitle>
        </CardHeader> */}
        <CardContent className="space-y-6">
          {/* 隐藏的文件输入框，在任何状态下都可用 */}
          <input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            onChange={handleFileInputChange}
            className="hidden"
          />
          {!image ? (
            // 上传区域
            <div
              className={cn(
                "border-2 border-dashed rounded-lg p-8 text-center transition-colors",
                isDragOver ? "border-primary bg-primary/5" : "border-muted-foreground/25",
                "hover:border-primary hover:bg-primary/5 cursor-pointer"
              )}
              onDrop={handleDrop}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              onClick={handleUploadClick}
            >
              <Upload className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
              <p className="text-lg font-medium mb-2">{t('upload_hint')}</p>
              <p className="text-sm text-muted-foreground mb-4">{t('supported_formats')}</p>
              <Button disabled={isUploading}>
                {isUploading ? t('processing') : t('upload_button')}
              </Button>
            </div>
          ) : (
            // 裁剪区域
            <div className="space-y-4">
              <div className="flex justify-center">
                <div className="relative w-full flex justify-center">
                  <ReactCrop
                    crop={crop}
                    onChange={(_, percentCrop) => setCrop(percentCrop)}
                    onComplete={(c) => setCompletedCrop(c)}
                    aspect={1}
                    circularCrop
                    keepSelection
                  >
                    <img
                      ref={imgRef}
                      src={image}
                      alt="Crop preview"
                      onLoad={onImageLoad}
                      style={{
                        maxWidth: imageDisplaySize.width || '100%',
                        maxHeight: imageDisplaySize.height || '70vh',
                        width: 'auto',
                        height: 'auto'
                      }}
                      className="object-contain"
                    />
                  </ReactCrop>
                </div>
              </div>

              {/* 缩放信息和控制 */}
              {scale < 1 && (
                <div className="text-center text-sm text-muted-foreground">
                  <p>{t('scaled_to')} {Math.round(scale * 100)}% {t('for_better_display')}</p>
                </div>
              )}
              
              {/* 操作按钮 */}
              <div className="flex justify-center gap-4">
                <Button
                  variant="outline"
                  onClick={handleUploadNew}
                  className="flex items-center gap-2"
                >
                  <Upload className="h-4 w-4" />
                  {t('reset_button')}
                </Button>
                <Button
                  onClick={handleDownload}
                  disabled={!completedCrop || isProcessing}
                  className="flex items-center gap-2"
                >
                  <Download className="h-4 w-4" />
                  {isProcessing ? t('processing') : t('download_button')}
                </Button>
              </div>
            </div>
          )}

          {/* 错误提示 */}
          {error && (
            <div className="text-center text-sm text-destructive bg-destructive/10 p-3 rounded-md">
              {error}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
